package api

import (
	"encoding/json"
	"net/http"

	"slices"
	"web-dashboard/backend/auth"
	"web-dashboard/backend/debug"
	"web-dashboard/backend/session"
)

// checkSessionPermission is a utility function to check if a user has specific permissions
// SECURITY: This function fails closed - any error or missing data results in denial
func checkSessionPermission(sessionData *session.SessionData, requiredPermissions ...string) bool {
	// SECURITY: Fail closed if session data is invalid
	if sessionData == nil {
		return false
	}

	// SECURITY: Fail closed if permissions array is nil or empty
	if sessionData.Permissions == nil {
		return false
	}

	// Debug logging to help diagnose permission issues
	debug.Debug("Checking permissions for user %s", sessionData.Email)
	debug.Debug("User permissions: %v", sessionData.Permissions)
	debug.Debug("Required permissions: %v", requiredPermissions)

	// Check for wildcard permission first - grants all access
	if slices.Contains(sessionData.Permissions, "*") {
		debug.Debug("Wildcard permission found - granting access")
		return true
	}

	// If no wildcard, check for specific permissions
	for _, reqPerm := range requiredPermissions {
		if slices.Contains(sessionData.Permissions, reqPerm) {
			debug.Debug("Found matching permission: %s", reqPerm)
			return true
		}
	}

	// SECURITY: Default to denial if no permissions match
	debug.Debug("No matching permissions found - denying access")
	return false
}

// requirePermissions creates middleware that checks for specific permissions
func requirePermissions(permissions ...string) func(http.HandlerFunc) http.HandlerFunc {
	return func(next http.HandlerFunc) http.HandlerFunc {
		return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			// Check if session data exists in context
			sessionValue := r.Context().Value(auth.SessionContextKey)
			if sessionValue == nil {
				w.Header().Set("Content-Type", "application/json")
				w.WriteHeader(http.StatusUnauthorized)
				json.NewEncoder(w).Encode(map[string]string{
					"error":   "unauthorized",
					"message": "Authentication required",
				})
				return
			}

			sessionData, ok := sessionValue.(*session.SessionData)
			if !ok {
				w.Header().Set("Content-Type", "application/json")
				w.WriteHeader(http.StatusUnauthorized)
				json.NewEncoder(w).Encode(map[string]string{
					"error":   "unauthorized",
					"message": "Invalid session data",
				})
				return
			}

			if !checkSessionPermission(sessionData, permissions...) {
				w.Header().Set("Content-Type", "application/json")
				w.WriteHeader(http.StatusForbidden)
				json.NewEncoder(w).Encode(map[string]string{
					"error":   "forbidden",
					"message": "Insufficient permissions",
				})
				return
			}

			next.ServeHTTP(w, r)
		})
	}
}

// Common permission middleware functions
var (
	RequireSystemSettings          = requirePermissions("System Settings - View", "System Settings - Manage Email", "System Settings - Full Access")
	RequireUserManagement          = requirePermissions("User Management - View", "User Management - Activate", "User Management - Enable", "User Management - Delete", "Admin")
	RequireRedisManagement         = requirePermissions("Redis Management - View", "Redis Management - Edit", "Redis Management - Delete", "Redis Management - Full Access")
	RequirePermissionManagement    = requirePermissions("Permission Management - View", "Permission Management - Create", "Permission Management - Edit", "Permission Management - Delete", "Permission Management - Full Access", "Admin") // Only for viewing permissions - create/delete done programmatically
	RequireSecurityGroupManagement = requirePermissions("User Management - View", "User Management - Activate", "User Management - Enable", "User Management - Delete", "Admin")
	RequireUserProfilePermission   = requirePermissions("*") // Any authenticated user can access their own profile
)
