package debug

import (
	"bytes"
	"log"
	"os"
	"testing"

	"web-dashboard/backend/config"

	"github.com/stretchr/testify/assert"
)

// captureLogOutput captures log output for testing
func captureLogOutput(f func()) string {
	var buf bytes.Buffer
	log.SetOutput(&buf)
	defer log.SetOutput(os.Stderr) // Restore default output

	f()

	return buf.String()
}

// Test logMessage function with different log levels
func TestLogMessage(t *testing.T) {
	// Save original environment
	originalLogLevel := os.Getenv("LOG_LEVEL")
	defer os.Setenv("LOG_LEVEL", originalLogLevel)

	t.Run("message logged when level is sufficient", func(t *testing.T) {
		os.Setenv("LOG_LEVEL", "DEBUG")

		output := captureLogOutput(func() {
			logMessage(config.ERROR, "Test error message")
		})

		assert.Contains(t, output, "[ERROR]")
		assert.Contains(t, output, "Test error message")
	})

	t.Run("message not logged when level is insufficient", func(t *testing.T) {
		os.Setenv("LOG_LEVEL", "ERROR")

		output := captureLogOutput(func() {
			logMessage(config.DEBUG, "Debug message should not appear")
		})

		assert.Empty(t, output)
	})

	t.Run("message formatting with arguments", func(t *testing.T) {
		os.Setenv("LOG_LEVEL", "TRACE")

		output := captureLogOutput(func() {
			logMessage(config.INFO, "User %s has %d items", "john", 5)
		})

		assert.Contains(t, output, "[INFO]")
		assert.Contains(t, output, "User john has 5 items")
	})

	t.Run("all log levels format correctly", func(t *testing.T) {
		os.Setenv("LOG_LEVEL", "TRACE")

		testCases := []struct {
			level    config.LogLevel
			expected string
		}{
			{config.TRACE, "[TRACE]"},
			{config.DEBUG, "[DEBUG]"},
			{config.INFO, "[INFO]"},
			{config.WARN, "[WARN]"},
			{config.ERROR, "[ERROR]"},
		}

		for _, tc := range testCases {
			output := captureLogOutput(func() {
				logMessage(tc.level, "test message")
			})

			assert.Contains(t, output, tc.expected)
			assert.Contains(t, output, "test message")
		}
	})
}

// Test Trace function
func TestTrace(t *testing.T) {
	// Save original environment
	originalLogLevel := os.Getenv("LOG_LEVEL")
	defer os.Setenv("LOG_LEVEL", originalLogLevel)

	t.Run("trace message logged at TRACE level", func(t *testing.T) {
		os.Setenv("LOG_LEVEL", "TRACE")

		output := captureLogOutput(func() {
			Trace("This is a trace message")
		})

		assert.Contains(t, output, "[TRACE]")
		assert.Contains(t, output, "This is a trace message")
	})

	t.Run("trace message not logged at higher levels", func(t *testing.T) {
		os.Setenv("LOG_LEVEL", "DEBUG")

		output := captureLogOutput(func() {
			Trace("This trace should not appear")
		})

		assert.Empty(t, output)
	})

	t.Run("trace with formatting", func(t *testing.T) {
		os.Setenv("LOG_LEVEL", "TRACE")

		output := captureLogOutput(func() {
			Trace("Trace: %s = %d", "count", 42)
		})

		assert.Contains(t, output, "[TRACE]")
		assert.Contains(t, output, "Trace: count = 42")
	})
}

// Test Debug function
func TestDebug(t *testing.T) {
	// Save original environment
	originalLogLevel := os.Getenv("LOG_LEVEL")
	defer os.Setenv("LOG_LEVEL", originalLogLevel)

	t.Run("debug message logged at DEBUG level", func(t *testing.T) {
		os.Setenv("LOG_LEVEL", "DEBUG")

		output := captureLogOutput(func() {
			Debug("This is a debug message")
		})

		assert.Contains(t, output, "[DEBUG]")
		assert.Contains(t, output, "This is a debug message")
	})

	t.Run("debug message not logged at higher levels", func(t *testing.T) {
		os.Setenv("LOG_LEVEL", "INFO")

		output := captureLogOutput(func() {
			Debug("This debug should not appear")
		})

		assert.Empty(t, output)
	})

	t.Run("debug with formatting", func(t *testing.T) {
		os.Setenv("LOG_LEVEL", "DEBUG")

		output := captureLogOutput(func() {
			Debug("Debug: processing %s with status %s", "file.txt", "success")
		})

		assert.Contains(t, output, "[DEBUG]")
		assert.Contains(t, output, "Debug: processing file.txt with status success")
	})
}

// Test Info function
func TestInfo(t *testing.T) {
	// Save original environment
	originalLogLevel := os.Getenv("LOG_LEVEL")
	defer os.Setenv("LOG_LEVEL", originalLogLevel)

	t.Run("info message logged at INFO level", func(t *testing.T) {
		os.Setenv("LOG_LEVEL", "INFO")

		output := captureLogOutput(func() {
			Info("This is an info message")
		})

		assert.Contains(t, output, "[INFO]")
		assert.Contains(t, output, "This is an info message")
	})

	t.Run("info message not logged at higher levels", func(t *testing.T) {
		os.Setenv("LOG_LEVEL", "WARN")

		output := captureLogOutput(func() {
			Info("This info should not appear")
		})

		assert.Empty(t, output)
	})

	t.Run("info with formatting", func(t *testing.T) {
		os.Setenv("LOG_LEVEL", "INFO")

		output := captureLogOutput(func() {
			Info("Server started on port %d", 8080)
		})

		assert.Contains(t, output, "[INFO]")
		assert.Contains(t, output, "Server started on port 8080")
	})
}

// Test Warn function
func TestWarn(t *testing.T) {
	// Save original environment
	originalLogLevel := os.Getenv("LOG_LEVEL")
	defer os.Setenv("LOG_LEVEL", originalLogLevel)

	t.Run("warn message logged at WARN level", func(t *testing.T) {
		os.Setenv("LOG_LEVEL", "WARN")

		output := captureLogOutput(func() {
			Warn("This is a warning message")
		})

		assert.Contains(t, output, "[WARN]")
		assert.Contains(t, output, "This is a warning message")
	})

	t.Run("warn message not logged at higher levels", func(t *testing.T) {
		os.Setenv("LOG_LEVEL", "ERROR")

		output := captureLogOutput(func() {
			Warn("This warning should not appear")
		})

		assert.Empty(t, output)
	})

	t.Run("warn with formatting", func(t *testing.T) {
		os.Setenv("LOG_LEVEL", "WARN")

		output := captureLogOutput(func() {
			Warn("Warning: %s is deprecated, use %s instead", "oldFunction", "newFunction")
		})

		assert.Contains(t, output, "[WARN]")
		assert.Contains(t, output, "Warning: oldFunction is deprecated, use newFunction instead")
	})
}

// Test Error function
func TestError(t *testing.T) {
	// Save original environment
	originalLogLevel := os.Getenv("LOG_LEVEL")
	defer os.Setenv("LOG_LEVEL", originalLogLevel)

	t.Run("error message always logged", func(t *testing.T) {
		os.Setenv("LOG_LEVEL", "ERROR")

		output := captureLogOutput(func() {
			Error("This is an error message")
		})

		assert.Contains(t, output, "[ERROR]")
		assert.Contains(t, output, "This is an error message")
	})

	t.Run("error with formatting", func(t *testing.T) {
		os.Setenv("LOG_LEVEL", "ERROR")

		output := captureLogOutput(func() {
			Error("Error: failed to connect to %s on port %d", "database", 5432)
		})

		assert.Contains(t, output, "[ERROR]")
		assert.Contains(t, output, "Error: failed to connect to database on port 5432")
	})
}

// Test GetCurrentLogLevel function
func TestGetCurrentLogLevel(t *testing.T) {
	// Save original environment
	originalLogLevel := os.Getenv("LOG_LEVEL")
	defer os.Setenv("LOG_LEVEL", originalLogLevel)

	t.Run("returns current log level", func(t *testing.T) {
		os.Setenv("LOG_LEVEL", "DEBUG")

		level := GetCurrentLogLevel()
		assert.Equal(t, "DEBUG", level)
	})

	t.Run("returns different log levels", func(t *testing.T) {
		testLevels := []string{"TRACE", "DEBUG", "INFO", "WARN", "ERROR"}

		for _, expectedLevel := range testLevels {
			os.Setenv("LOG_LEVEL", expectedLevel)

			level := GetCurrentLogLevel()
			assert.Equal(t, expectedLevel, level)
		}
	})
}

// Test log level hierarchy
func TestLogLevelHierarchy(t *testing.T) {
	// Save original environment
	originalLogLevel := os.Getenv("LOG_LEVEL")
	defer os.Setenv("LOG_LEVEL", originalLogLevel)

	t.Run("log level hierarchy works correctly", func(t *testing.T) {
		// Set to INFO level
		os.Setenv("LOG_LEVEL", "INFO")

		var outputs []string

		// Test each logging function
		outputs = append(outputs, captureLogOutput(func() { Trace("trace") }))
		outputs = append(outputs, captureLogOutput(func() { Debug("debug") }))
		outputs = append(outputs, captureLogOutput(func() { Info("info") }))
		outputs = append(outputs, captureLogOutput(func() { Warn("warn") }))
		outputs = append(outputs, captureLogOutput(func() { Error("error") }))

		// TRACE and DEBUG should be empty (below INFO level)
		assert.Empty(t, outputs[0]) // TRACE
		assert.Empty(t, outputs[1]) // DEBUG

		// INFO, WARN, and ERROR should have content (at or above INFO level)
		assert.NotEmpty(t, outputs[2]) // INFO
		assert.NotEmpty(t, outputs[3]) // WARN
		assert.NotEmpty(t, outputs[4]) // ERROR

		// Verify correct prefixes
		assert.Contains(t, outputs[2], "[INFO]")
		assert.Contains(t, outputs[3], "[WARN]")
		assert.Contains(t, outputs[4], "[ERROR]")
	})
}

// Test edge cases and special scenarios
func TestLoggingEdgeCases(t *testing.T) {
	// Save original environment
	originalLogLevel := os.Getenv("LOG_LEVEL")
	defer os.Setenv("LOG_LEVEL", originalLogLevel)

	t.Run("empty message", func(t *testing.T) {
		os.Setenv("LOG_LEVEL", "INFO")

		output := captureLogOutput(func() {
			Info("")
		})

		assert.Contains(t, output, "[INFO]")
		// Should still have the prefix even with empty message
	})

	t.Run("message with special characters", func(t *testing.T) {
		os.Setenv("LOG_LEVEL", "INFO")

		output := captureLogOutput(func() {
			Info("Special chars: %% \n \t \" ' \\")
		})

		assert.Contains(t, output, "[INFO]")
		assert.Contains(t, output, "Special chars:")
	})

	t.Run("no formatting arguments", func(t *testing.T) {
		os.Setenv("LOG_LEVEL", "INFO")

		output := captureLogOutput(func() {
			Info("Simple message without formatting")
		})

		assert.Contains(t, output, "[INFO]")
		assert.Contains(t, output, "Simple message without formatting")
	})

	t.Run("multiple arguments", func(t *testing.T) {
		os.Setenv("LOG_LEVEL", "INFO")

		output := captureLogOutput(func() {
			Info("Multiple: %s, %d, %t, %f", "string", 42, true, 3.14)
		})

		assert.Contains(t, output, "[INFO]")
		assert.Contains(t, output, "Multiple: string, 42, true, 3.14")
	})
}
