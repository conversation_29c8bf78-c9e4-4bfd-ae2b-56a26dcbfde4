package users

import (
	"net/http/httptest"
	"os"
	"strings"
	"testing"

	"web-dashboard/backend/database"
	"web-dashboard/backend/testutils"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestMain(m *testing.M) {
	testutils.SetupTestEnvironment()

	// Initialize database for tests
	err := database.InitDB()
	if err != nil {
		panic("Failed to initialize database for tests: " + err.Error())
	}

	// Run tests
	code := m.Run()

	// Cleanup
	if database.DB != nil {
		database.DB.Close()
	}

	os.Exit(code)
}

func TestGetUserByID(t *testing.T) {
	// Create a test user first
	userID, err := CreateUser("<EMAIL>", "password123", false, true)
	require.NoError(t, err)
	defer func() {
		database.DB.Exec("DELETE FROM users WHERE id = $1", userID)
	}()

	t.Run("existing user", func(t *testing.T) {
		user, err := GetUserByID(userID)
		require.NoError(t, err)
		assert.Equal(t, userID, user.ID)
		assert.Equal(t, "<EMAIL>", user.Email)
		assert.True(t, user.Enabled)
		assert.Empty(t, user.PasswordHash) // Should not include password hash
	})

	t.Run("non-existent user", func(t *testing.T) {
		user, err := GetUserByID(99999)
		assert.Error(t, err)
		assert.Nil(t, user)
		assert.Contains(t, err.Error(), "user not found")
	})
}

func TestGetUserByEmail(t *testing.T) {
	// Create a test user first
	userID, err := CreateUser("<EMAIL>", "password123", false, true)
	require.NoError(t, err)
	defer func() {
		database.DB.Exec("DELETE FROM users WHERE id = $1", userID)
	}()

	t.Run("existing user", func(t *testing.T) {
		user, err := GetUserByEmail("<EMAIL>")
		require.NoError(t, err)
		assert.Equal(t, "<EMAIL>", user.Email)
		assert.True(t, user.Enabled)
		assert.Empty(t, user.PasswordHash) // Should not include password hash
	})

	t.Run("non-existent user", func(t *testing.T) {
		user, err := GetUserByEmail("<EMAIL>")
		assert.Error(t, err)
		assert.Nil(t, user)
		assert.Contains(t, err.Error(), "user not found")
	})
}

func TestGetUserByEmailWithPassword(t *testing.T) {
	// Create a test user first
	userID, err := CreateUser("<EMAIL>", "password123", false, true)
	require.NoError(t, err)
	defer func() {
		database.DB.Exec("DELETE FROM users WHERE id = $1", userID)
	}()

	t.Run("existing user", func(t *testing.T) {
		user, err := GetUserByEmailWithPassword("<EMAIL>")
		require.NoError(t, err)
		assert.Equal(t, "<EMAIL>", user.Email)
		assert.True(t, user.Enabled)
		assert.NotEmpty(t, user.PasswordHash) // Should include password hash
	})

	t.Run("non-existent user", func(t *testing.T) {
		user, err := GetUserByEmailWithPassword("<EMAIL>")
		assert.Error(t, err)
		assert.Nil(t, user)
		assert.Contains(t, err.Error(), "user not found")
	})
}

func TestGetAllUsers(t *testing.T) {
	// Create test users
	userID1, err := CreateUser("<EMAIL>", "password123", false, true)
	require.NoError(t, err)
	userID2, err := CreateUser("<EMAIL>", "password123", false, false)
	require.NoError(t, err)
	defer func() {
		database.DB.Exec("DELETE FROM users WHERE id IN ($1, $2)", userID1, userID2)
	}()

	users, err := GetAllUsers()
	require.NoError(t, err)
	assert.GreaterOrEqual(t, len(users), 2) // At least our test users

	// Find our test users
	var foundUser1, foundUser2 bool
	for _, user := range users {
		if user.Email == "<EMAIL>" {
			foundUser1 = true
			assert.True(t, user.Enabled)
		}
		if user.Email == "<EMAIL>" {
			foundUser2 = true
			assert.False(t, user.Enabled)
		}
	}
	assert.True(t, foundUser1)
	assert.True(t, foundUser2)
}

func TestGenerateComplexPassword(t *testing.T) {
	password := generateComplexPassword()
	assert.Len(t, password, 32)
	assert.NotEmpty(t, password)

	// Generate multiple passwords to ensure they're different
	password2 := generateComplexPassword()
	assert.NotEqual(t, password, password2)
}

func TestHashPassword(t *testing.T) {
	password := "testpassword123"
	hash, err := hashPassword(password)
	require.NoError(t, err)
	assert.NotEmpty(t, hash)
	assert.Contains(t, hash, ":") // Should contain salt:hash format

	// Same password should produce different hashes due to random salt
	hash2, err := hashPassword(password)
	require.NoError(t, err)
	assert.NotEqual(t, hash, hash2)
}

func TestCreateUser(t *testing.T) {
	t.Run("successful creation", func(t *testing.T) {
		userID, err := CreateUser("<EMAIL>", "password123", false, true)
		require.NoError(t, err)
		assert.Greater(t, userID, 0)
		defer func() {
			database.DB.Exec("DELETE FROM users WHERE id = $1", userID)
		}()

		// Verify user was created
		user, err := GetUserByID(userID)
		require.NoError(t, err)
		assert.Equal(t, "<EMAIL>", user.Email)
		assert.True(t, user.Enabled)
	})

	t.Run("duplicate email", func(t *testing.T) {
		// Create first user
		userID, err := CreateUser("<EMAIL>", "password123", false, true)
		require.NoError(t, err)
		defer func() {
			database.DB.Exec("DELETE FROM users WHERE id = $1", userID)
		}()

		// Try to create duplicate
		_, err = CreateUser("<EMAIL>", "password456", false, true)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "already exists")
	})
}

func TestCreateOrUpdateUser(t *testing.T) {
	t.Run("create new user", func(t *testing.T) {
		user, err := CreateOrUpdateUser("<EMAIL>", "google")
		require.NoError(t, err)
		assert.Equal(t, "<EMAIL>", user.Email)
		assert.Equal(t, "google", user.LastLoginMethod)
		assert.True(t, user.Enabled)
		defer func() {
			database.DB.Exec("DELETE FROM users WHERE id = $1", user.ID)
		}()
	})

	t.Run("update existing user", func(t *testing.T) {
		// Create user first
		userID, err := CreateUser("<EMAIL>", "password123", false, true)
		require.NoError(t, err)
		defer func() {
			database.DB.Exec("DELETE FROM users WHERE id = $1", userID)
		}()

		// Update with OAuth login
		user, err := CreateOrUpdateUser("<EMAIL>", "github")
		require.NoError(t, err)
		assert.Equal(t, userID, user.ID)

		// Note: CreateOrUpdateUser returns the original user object, not the updated one
		// To verify the update worked, we need to fetch the user again
		updatedUser, err := GetUserByID(userID)
		require.NoError(t, err)
		assert.Equal(t, "github", updatedUser.LastLoginMethod)
	})
}

func TestEnableUser(t *testing.T) {
	// Create disabled user
	userID, err := CreateUser("<EMAIL>", "password123", false, false)
	require.NoError(t, err)
	defer func() {
		database.DB.Exec("DELETE FROM users WHERE id = $1", userID)
	}()

	t.Run("enable existing user", func(t *testing.T) {
		err := EnableUser(userID)
		require.NoError(t, err)

		// Verify user is enabled
		user, err := GetUserByID(userID)
		require.NoError(t, err)
		assert.True(t, user.Enabled)
	})

	t.Run("enable non-existent user", func(t *testing.T) {
		err := EnableUser(99999)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "user not found")
	})
}

func TestDisableUser(t *testing.T) {
	// Create enabled user
	userID, err := CreateUser("<EMAIL>", "password123", false, true)
	require.NoError(t, err)
	defer func() {
		database.DB.Exec("DELETE FROM users WHERE id = $1", userID)
	}()

	t.Run("disable existing user", func(t *testing.T) {
		err := DisableUser(userID)
		require.NoError(t, err)

		// Verify user is disabled
		user, err := GetUserByID(userID)
		require.NoError(t, err)
		assert.False(t, user.Enabled)
	})

	t.Run("disable non-existent user", func(t *testing.T) {
		err := DisableUser(99999)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "user not found")
	})
}

func TestDeleteUser(t *testing.T) {
	t.Run("delete existing user", func(t *testing.T) {
		userID, err := CreateUser("<EMAIL>", "password123", false, true)
		require.NoError(t, err)

		err = DeleteUser(userID)
		require.NoError(t, err)

		// Verify user is deleted
		user, err := GetUserByID(userID)
		assert.Error(t, err)
		assert.Nil(t, user)
	})

	t.Run("delete non-existent user", func(t *testing.T) {
		err := DeleteUser(99999)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "user not found")
	})
}

func TestGetCurrentUserEmail(t *testing.T) {
	t.Run("no session cookie", func(t *testing.T) {
		req := httptest.NewRequest("GET", "/", nil)
		email, err := GetCurrentUserEmail(req)
		assert.Error(t, err)
		assert.Empty(t, email)
		assert.Contains(t, err.Error(), "not authenticated")
	})

	t.Run("invalid session cookie", func(t *testing.T) {
		// Skip this test if session manager is not initialized
		// This would require Redis to be running and session manager to be set up
		t.Skip("Session manager not initialized in test environment")
	})
}

func TestGetCurrentUser(t *testing.T) {
	t.Run("no session cookie", func(t *testing.T) {
		req := httptest.NewRequest("GET", "/", nil)
		user, err := GetCurrentUser(req)
		assert.Error(t, err)
		assert.Nil(t, user)
	})
}

func TestGetSuperAdminEmails(t *testing.T) {
	// This test requires the security groups and user_security_groups tables
	// to be properly set up. In a real test environment, you would:
	// 1. Create a Super Admin security group
	// 2. Create test users
	// 3. Assign users to the Super Admin group
	// 4. Test the function

	t.Run("get super admin emails", func(t *testing.T) {
		// Skip this test as it requires complex database setup with security groups
		t.Skip("Requires security groups tables to be set up - tested in integration tests")
	})
}

// Test helper functions for password validation
func TestPasswordHashing(t *testing.T) {
	t.Run("password hashing consistency", func(t *testing.T) {
		password := "testpassword123"

		// Hash the same password multiple times
		hash1, err1 := hashPassword(password)
		hash2, err2 := hashPassword(password)

		require.NoError(t, err1)
		require.NoError(t, err2)

		// Hashes should be different due to random salt
		assert.NotEqual(t, hash1, hash2)

		// Both should contain salt:hash format
		assert.Contains(t, hash1, ":")
		assert.Contains(t, hash2, ":")

		// Both parts should be hex encoded
		parts1 := strings.Split(hash1, ":")
		parts2 := strings.Split(hash2, ":")
		assert.Len(t, parts1, 2)
		assert.Len(t, parts2, 2)

		// Salt should be 32 hex characters (16 bytes * 2)
		assert.Len(t, parts1[0], 32)
		assert.Len(t, parts2[0], 32)

		// Hash should be 64 hex characters (32 bytes * 2)
		assert.Len(t, parts1[1], 64)
		assert.Len(t, parts2[1], 64)
	})
}

// Test edge cases and error conditions
func TestUserEdgeCases(t *testing.T) {
	t.Run("empty email", func(t *testing.T) {
		userID, err := CreateUser("", "password123", false, true)
		// This might succeed in SQLite but should be handled by application logic
		// If it succeeds, clean up the user
		if err == nil && userID > 0 {
			database.DB.Exec("DELETE FROM users WHERE id = $1", userID)
			t.Log("Empty email was allowed by database - consider adding validation")
		}
		// Either way, the test passes as we're just checking behavior
	})

	t.Run("empty password", func(t *testing.T) {
		_, err := CreateUser("<EMAIL>", "", false, true)
		// Should still work as it will hash the empty string
		require.NoError(t, err)
		defer func() {
			database.DB.Exec("DELETE FROM users WHERE email = $1", "<EMAIL>")
		}()
	})

	t.Run("very long email", func(t *testing.T) {
		longEmail := strings.Repeat("a", 250) + "@example.com"
		_, err := CreateUser(longEmail, "password123", false, true)
		// Should be handled by database constraints
		assert.Error(t, err)
	})
}
