package security

import (
	"os"
	"testing"

	"web-dashboard/backend/database"
	"web-dashboard/backend/testutils"

	"github.com/stretchr/testify/assert"
)

func TestMain(m *testing.M) {
	testutils.SetupTestEnvironment()

	// Initialize database for tests
	err := database.InitDB()
	if err != nil {
		panic("Failed to initialize database for tests: " + err.Error())
	}

	// Run tests
	code := m.Run()

	// Cleanup
	if database.DB != nil {
		database.DB.Close()
	}

	os.Exit(code)
}

// Test Permission struct and basic operations
func TestPermissionStruct(t *testing.T) {
	t.Run("permission creation", func(t *testing.T) {
		permission := Permission{
			ID:          1,
			Name:        "Read Access",
			Description: "Allows reading data",
			Resource:    "users",
			Action:      "read",
			CreatedAt:   "2023-01-01T00:00:00Z",
			UpdatedAt:   "2023-01-01T00:00:00Z",
		}

		assert.Equal(t, 1, permission.ID)
		assert.Equal(t, "Read Access", permission.Name)
		assert.Equal(t, "Allows reading data", permission.Description)
		assert.Equal(t, "users", permission.Resource)
		assert.Equal(t, "read", permission.Action)
		assert.NotEmpty(t, permission.CreatedAt)
		assert.NotEmpty(t, permission.UpdatedAt)
	})
}

// Test SecurityGroup struct and basic operations
func TestSecurityGroupStruct(t *testing.T) {
	t.Run("security group creation", func(t *testing.T) {
		group := SecurityGroup{
			ID:          1,
			Name:        "Administrators",
			Description: "System administrators",
			CreatedAt:   "2023-01-01T00:00:00Z",
			UpdatedAt:   "2023-01-01T00:00:00Z",
		}

		assert.Equal(t, 1, group.ID)
		assert.Equal(t, "Administrators", group.Name)
		assert.Equal(t, "System administrators", group.Description)
		assert.NotEmpty(t, group.CreatedAt)
		assert.NotEmpty(t, group.UpdatedAt)
	})
}

// Test database-dependent functions with error handling
func TestGetAllPermissions(t *testing.T) {
	t.Run("get all permissions", func(t *testing.T) {
		permissions, err := GetAllPermissions()
		// Should either return permissions or error due to missing tables
		if err != nil {
			assert.Error(t, err)
			assert.Nil(t, permissions)
			t.Logf("GetAllPermissions failed (expected in test env): %v", err)
		} else {
			assert.NotNil(t, permissions)
			assert.GreaterOrEqual(t, len(permissions), 0)
		}
	})
}

func TestCreatePermission(t *testing.T) {
	t.Run("create permission with missing tables", func(t *testing.T) {
		permission, err := CreatePermission("Test Permission", "Test description", "test", "read")
		// Should error due to missing tables in test environment
		if err != nil {
			assert.Error(t, err)
			assert.Nil(t, permission)
			t.Logf("CreatePermission failed (expected in test env): %v", err)
		} else {
			// If it succeeds, clean up
			assert.NotNil(t, permission)
			t.Log("CreatePermission succeeded - test database has permissions table")
		}
	})
}

func TestGetPermission(t *testing.T) {
	t.Run("get non-existent permission", func(t *testing.T) {
		permission, err := GetPermission(99999)
		// Should error due to missing tables or non-existent permission
		assert.Error(t, err)
		assert.Nil(t, permission)
	})
}

func TestGetAllSecurityGroups(t *testing.T) {
	t.Run("get all security groups", func(t *testing.T) {
		groups, err := GetAllSecurityGroups()
		// Should either return groups or error due to missing tables
		if err != nil {
			assert.Error(t, err)
			assert.Nil(t, groups)
			t.Logf("GetAllSecurityGroups failed (expected in test env): %v", err)
		} else {
			assert.NotNil(t, groups)
			assert.GreaterOrEqual(t, len(groups), 0)
		}
	})
}

func TestCreateSecurityGroup(t *testing.T) {
	t.Run("create security group with missing tables", func(t *testing.T) {
		group, err := CreateSecurityGroup("Test Group", "Test description")
		// Should error due to missing tables in test environment
		if err != nil {
			assert.Error(t, err)
			assert.Nil(t, group)
			t.Logf("CreateSecurityGroup failed (expected in test env): %v", err)
		} else {
			// If it succeeds, clean up
			assert.NotNil(t, group)
			t.Log("CreateSecurityGroup succeeded - test database has security_groups table")
		}
	})
}

func TestGetSecurityGroup(t *testing.T) {
	t.Run("get non-existent security group", func(t *testing.T) {
		group, err := GetSecurityGroup(99999)
		// Should error due to missing tables or non-existent group
		assert.Error(t, err)
		assert.Nil(t, group)
	})
}

func TestUpdateSecurityGroup(t *testing.T) {
	t.Run("update non-existent security group", func(t *testing.T) {
		err := UpdateSecurityGroup(99999, "Updated Name", "Updated description")
		// Should error due to missing tables or non-existent group
		assert.Error(t, err)
	})
}

func TestDeleteSecurityGroup(t *testing.T) {
	t.Run("delete non-existent security group", func(t *testing.T) {
		err := DeleteSecurityGroup(99999)
		// Should error due to missing tables or non-existent group
		assert.Error(t, err)
	})
}

func TestGetUserPermissions(t *testing.T) {
	t.Run("get permissions for non-existent user", func(t *testing.T) {
		permissions, err := GetUserPermissions(99999)
		// Should either return empty permissions or error due to missing tables
		if err != nil {
			assert.Error(t, err)
			assert.Nil(t, permissions)
		} else {
			// When successful, should return empty slice (not nil) if no permissions
			assert.NoError(t, err)
			assert.GreaterOrEqual(t, len(permissions), 0)
		}
	})
}

func TestGetUserSecurityGroups(t *testing.T) {
	t.Run("get security groups for non-existent user", func(t *testing.T) {
		groups, err := GetUserSecurityGroups(99999)
		// Should either return empty groups or error due to missing tables
		if err != nil {
			assert.Error(t, err)
			assert.Nil(t, groups)
		} else {
			// When successful, should return empty slice (not nil) if no groups
			assert.NoError(t, err)
			assert.GreaterOrEqual(t, len(groups), 0)
		}
	})
}

func TestUserHasPermission(t *testing.T) {
	t.Run("check permission for non-existent user", func(t *testing.T) {
		hasPermission, err := UserHasPermission(99999, "test", "read")
		// Should either return false or error due to missing tables
		if err != nil {
			assert.Error(t, err)
			assert.False(t, hasPermission)
		} else {
			assert.False(t, hasPermission) // Non-existent user should not have permissions
		}
	})
}

func TestAddUserToSecurityGroup(t *testing.T) {
	t.Run("add user to non-existent security group", func(t *testing.T) {
		err := AddUserToSecurityGroup(99999, 99999)
		// Should error due to missing tables or non-existent entities
		assert.Error(t, err)
	})
}

func TestRemoveUserFromSecurityGroup(t *testing.T) {
	t.Run("remove user from non-existent security group", func(t *testing.T) {
		err := RemoveUserFromSecurityGroup(99999, 99999)
		// Should error due to missing tables or non-existent entities
		assert.Error(t, err)
	})
}

func TestGetSecurityGroupMembers(t *testing.T) {
	t.Run("get members of non-existent security group", func(t *testing.T) {
		members, err := GetSecurityGroupMembers(99999)
		// Should either return empty members or error due to missing tables
		if err != nil {
			assert.Error(t, err)
			assert.Nil(t, members)
		} else {
			// When successful, should return empty slice (not nil) if no members
			assert.NoError(t, err)
			assert.GreaterOrEqual(t, len(members), 0)
		}
	})
}

func TestGetSecurityGroupPermissions(t *testing.T) {
	t.Run("get permissions of non-existent security group", func(t *testing.T) {
		permissions, err := GetSecurityGroupPermissions(99999)
		// Should either return empty permissions or error due to missing tables
		if err != nil {
			assert.Error(t, err)
			assert.Nil(t, permissions)
		} else {
			// When successful, should return empty slice (not nil) if no permissions
			assert.NoError(t, err)
			assert.GreaterOrEqual(t, len(permissions), 0)
		}
	})
}

func TestAddPermissionToSecurityGroup(t *testing.T) {
	t.Run("add permission to non-existent security group", func(t *testing.T) {
		err := AddPermissionToSecurityGroup(99999, 99999)
		// Should error due to missing tables or non-existent entities
		assert.Error(t, err)
	})
}

func TestRemovePermissionFromSecurityGroup(t *testing.T) {
	t.Run("remove permission from non-existent security group", func(t *testing.T) {
		err := RemovePermissionFromSecurityGroup(99999, 99999)
		// Should error due to missing tables or non-existent entities
		assert.Error(t, err)
	})
}

func TestEnsureUserInDefaultGroup(t *testing.T) {
	t.Run("ensure non-existent user in default group", func(t *testing.T) {
		err := EnsureUserInDefaultGroup(99999, "<EMAIL>")
		// Should error due to missing tables or non-existent user
		assert.Error(t, err)
	})
}

// Test validation and edge cases
func TestSecurityValidation(t *testing.T) {
	t.Run("empty permission name", func(t *testing.T) {
		permission, err := CreatePermission("", "description", "resource", "action")
		// Should handle empty name appropriately
		if err != nil {
			assert.Error(t, err)
			assert.Nil(t, permission)
		}
	})

	t.Run("empty security group name", func(t *testing.T) {
		group, err := CreateSecurityGroup("", "description")
		// Should handle empty name appropriately
		if err != nil {
			assert.Error(t, err)
			assert.Nil(t, group)
		}
	})
}
