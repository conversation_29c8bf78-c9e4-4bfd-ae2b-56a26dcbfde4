package email

import (
	"os"
	"testing"

	"github.com/stretchr/testify/assert"
)

// Test EmailMessage struct
func TestEmailMessage(t *testing.T) {
	t.Run("email message structure", func(t *testing.T) {
		msg := EmailMessage{
			To:      []string{"<EMAIL>", "<EMAIL>"},
			Subject: "Test Subject",
			Body:    "Test body content",
			IsHTML:  true,
		}

		assert.Len(t, msg.To, 2)
		assert.Contains(t, msg.To, "<EMAIL>")
		assert.Contains(t, msg.To, "<EMAIL>")
		assert.Equal(t, "Test Subject", msg.Subject)
		assert.Equal(t, "Test body content", msg.Body)
		assert.True(t, msg.IsHTML)
	})

	t.Run("plain text email", func(t *testing.T) {
		msg := EmailMessage{
			To:      []string{"<EMAIL>"},
			Subject: "Plain Text Email",
			Body:    "This is plain text content",
			IsHTML:  false,
		}

		assert.False(t, msg.IsHTML)
		assert.Equal(t, "This is plain text content", msg.Body)
		assert.Equal(t, "Plain Text Email", msg.Subject)
		assert.Contains(t, msg.To, "<EMAIL>")
	})
}

// Test NewGmailService function
func TestNewGmailService(t *testing.T) {
	// Save original environment variables
	originalServiceEmail := os.Getenv("GMAIL_SERVICE_ACCOUNT_EMAIL")
	originalPrivateKey := os.Getenv("GMAIL_PRIVATE_KEY")
	originalClientEmail := os.Getenv("GMAIL_CLIENT_EMAIL")
	originalTokenURI := os.Getenv("GMAIL_TOKEN_URI")

	defer func() {
		os.Setenv("GMAIL_SERVICE_ACCOUNT_EMAIL", originalServiceEmail)
		os.Setenv("GMAIL_PRIVATE_KEY", originalPrivateKey)
		os.Setenv("GMAIL_CLIENT_EMAIL", originalClientEmail)
		os.Setenv("GMAIL_TOKEN_URI", originalTokenURI)
	}()

	t.Run("missing environment variables", func(t *testing.T) {
		os.Unsetenv("GMAIL_SERVICE_ACCOUNT_EMAIL")
		os.Unsetenv("GMAIL_PRIVATE_KEY")
		os.Unsetenv("GMAIL_CLIENT_EMAIL")

		service, err := NewGmailService()
		assert.Error(t, err)
		assert.Nil(t, service)
		assert.Contains(t, err.Error(), "missing required Gmail service account environment variables")
	})

	t.Run("missing service account email", func(t *testing.T) {
		os.Unsetenv("GMAIL_SERVICE_ACCOUNT_EMAIL")
		os.Setenv("GMAIL_PRIVATE_KEY", "test-key")
		os.Setenv("GMAIL_CLIENT_EMAIL", "<EMAIL>")

		service, err := NewGmailService()
		assert.Error(t, err)
		assert.Nil(t, service)
		assert.Contains(t, err.Error(), "missing required Gmail service account environment variables")
	})

	t.Run("missing private key", func(t *testing.T) {
		os.Setenv("GMAIL_SERVICE_ACCOUNT_EMAIL", "<EMAIL>")
		os.Unsetenv("GMAIL_PRIVATE_KEY")
		os.Setenv("GMAIL_CLIENT_EMAIL", "<EMAIL>")

		service, err := NewGmailService()
		assert.Error(t, err)
		assert.Nil(t, service)
		assert.Contains(t, err.Error(), "missing required Gmail service account environment variables")
	})

	t.Run("missing client email", func(t *testing.T) {
		os.Setenv("GMAIL_SERVICE_ACCOUNT_EMAIL", "<EMAIL>")
		os.Setenv("GMAIL_PRIVATE_KEY", "test-key")
		os.Unsetenv("GMAIL_CLIENT_EMAIL")

		service, err := NewGmailService()
		assert.Error(t, err)
		assert.Nil(t, service)
		assert.Contains(t, err.Error(), "missing required Gmail service account environment variables")
	})

	t.Run("default token URI", func(t *testing.T) {
		os.Setenv("GMAIL_SERVICE_ACCOUNT_EMAIL", "<EMAIL>")
		os.Setenv("GMAIL_PRIVATE_KEY", "invalid-key-for-test")
		os.Setenv("GMAIL_CLIENT_EMAIL", "<EMAIL>")
		os.Unsetenv("GMAIL_TOKEN_URI")

		// This will fail due to invalid key, but we can test that default token URI is used
		_, err := NewGmailService()
		assert.Error(t, err)
		// Should fail at key parsing, not at missing token URI
		assert.Contains(t, err.Error(), "failed to parse private key")
	})

	t.Run("invalid private key format", func(t *testing.T) {
		os.Setenv("GMAIL_SERVICE_ACCOUNT_EMAIL", "<EMAIL>")
		os.Setenv("GMAIL_PRIVATE_KEY", "not-a-valid-pem-key")
		os.Setenv("GMAIL_CLIENT_EMAIL", "<EMAIL>")
		os.Setenv("GMAIL_TOKEN_URI", "https://oauth2.googleapis.com/token")

		service, err := NewGmailService()
		assert.Error(t, err)
		assert.Nil(t, service)
		assert.Contains(t, err.Error(), "failed to parse private key")
	})
}

// Test parsePrivateKey function
func TestParsePrivateKey(t *testing.T) {
	t.Run("invalid PEM format", func(t *testing.T) {
		invalidKey := "not-a-pem-key"

		key, err := parsePrivateKey(invalidKey)
		assert.Error(t, err)
		assert.Nil(t, key)
		assert.Contains(t, err.Error(), "failed to parse PEM block")
	})

	t.Run("empty key", func(t *testing.T) {
		key, err := parsePrivateKey("")
		assert.Error(t, err)
		assert.Nil(t, key)
		assert.Contains(t, err.Error(), "failed to parse PEM block")
	})

	t.Run("key with escaped newlines", func(t *testing.T) {
		// Test that the function handles \n escape sequences
		keyWithEscapes := "-----BEGIN PRIVATE KEY-----\\nMIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQC..."

		// This will still fail because it's not a real key, but we can test the newline replacement
		_, err := parsePrivateKey(keyWithEscapes)
		assert.Error(t, err)
		// Should fail at PEM parsing since it's not a valid PEM block
		assert.Contains(t, err.Error(), "failed to parse PEM block")
	})

	t.Run("valid PEM structure but invalid key data", func(t *testing.T) {
		invalidPEM := `-----BEGIN PRIVATE KEY-----
MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQC
-----END PRIVATE KEY-----`

		key, err := parsePrivateKey(invalidPEM)
		assert.Error(t, err)
		assert.Nil(t, key)
		// This should fail at key parsing since the data is invalid
		assert.True(t, err != nil)
	})
}

// Test GmailService methods with mock scenarios
func TestGmailService_SendEmail(t *testing.T) {
	// Since we can't easily test actual Gmail API calls without credentials,
	// we test the validation and error handling

	t.Run("empty recipients", func(t *testing.T) {
		// Create a mock service (this won't actually work without real credentials)
		service := &GmailService{
			fromEmail: "<EMAIL>",
		}

		msg := EmailMessage{
			To:      []string{}, // Empty recipients
			Subject: "Test",
			Body:    "Test body",
			IsHTML:  false,
		}

		err := service.SendEmail(msg)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "no recipients specified")
	})

	t.Run("valid message structure", func(t *testing.T) {
		// Test message validation without calling the actual service
		msg := EmailMessage{
			To:      []string{"<EMAIL>"},
			Subject: "Test Subject",
			Body:    "Test body content",
			IsHTML:  true,
		}

		// Validate the message structure
		assert.NotEmpty(t, msg.To)
		assert.NotEmpty(t, msg.Subject)
		assert.NotEmpty(t, msg.Body)
		assert.True(t, msg.IsHTML)

		// Test that we can create a Gmail message from it
		service := &GmailService{
			fromEmail: "<EMAIL>",
		}
		gmailMsg := service.createGmailMessage(msg)
		assert.NotNil(t, gmailMsg)
		assert.NotEmpty(t, gmailMsg.Raw)
	})
}

// Test createGmailMessage method
func TestGmailService_CreateGmailMessage(t *testing.T) {
	service := &GmailService{
		fromEmail: "<EMAIL>",
	}

	t.Run("plain text message", func(t *testing.T) {
		msg := EmailMessage{
			To:      []string{"<EMAIL>"},
			Subject: "Plain Text Test",
			Body:    "This is a plain text message",
			IsHTML:  false,
		}

		gmailMsg := service.createGmailMessage(msg)

		assert.NotNil(t, gmailMsg)
		assert.NotEmpty(t, gmailMsg.Raw)

		// Decode and check the message content
		// The Raw field is base64url encoded
		// We can't easily decode it here, but we can verify it's not empty
	})

	t.Run("HTML message", func(t *testing.T) {
		msg := EmailMessage{
			To:      []string{"<EMAIL>"},
			Subject: "HTML Test",
			Body:    "<h1>This is HTML content</h1>",
			IsHTML:  true,
		}

		gmailMsg := service.createGmailMessage(msg)

		assert.NotNil(t, gmailMsg)
		assert.NotEmpty(t, gmailMsg.Raw)
	})

	t.Run("multiple recipients", func(t *testing.T) {
		msg := EmailMessage{
			To:      []string{"<EMAIL>", "<EMAIL>", "<EMAIL>"},
			Subject: "Multiple Recipients",
			Body:    "Message to multiple recipients",
			IsHTML:  false,
		}

		gmailMsg := service.createGmailMessage(msg)

		assert.NotNil(t, gmailMsg)
		assert.NotEmpty(t, gmailMsg.Raw)
	})

	t.Run("special characters in subject and body", func(t *testing.T) {
		msg := EmailMessage{
			To:      []string{"<EMAIL>"},
			Subject: "Special chars: àáâãäå ñ ç ü",
			Body:    "Body with special characters: €£¥ 中文 русский",
			IsHTML:  false,
		}

		gmailMsg := service.createGmailMessage(msg)

		assert.NotNil(t, gmailMsg)
		assert.NotEmpty(t, gmailMsg.Raw)
	})
}

// Test SendTestEmail method
func TestGmailService_SendTestEmail(t *testing.T) {
	t.Run("test email message creation", func(t *testing.T) {
		// Test that SendTestEmail creates the correct message structure
		// We can't test the actual sending without a real service
		service := &GmailService{
			fromEmail: "<EMAIL>",
		}

		// Test the message that would be created
		recipient := "<EMAIL>"

		// Manually create what SendTestEmail would create
		msg := EmailMessage{
			To:      []string{recipient},
			Subject: "Test Email from Web Dashboard",
			Body:    "This is a test email to verify that the Gmail integration is working correctly using the Gmail API v1.",
			IsHTML:  false,
		}

		assert.Equal(t, []string{recipient}, msg.To)
		assert.Equal(t, "Test Email from Web Dashboard", msg.Subject)
		assert.Contains(t, msg.Body, "test email")
		assert.False(t, msg.IsHTML)

		// Test that we can create a Gmail message from it
		gmailMsg := service.createGmailMessage(msg)
		assert.NotNil(t, gmailMsg)
		assert.NotEmpty(t, gmailMsg.Raw)
	})

	t.Run("empty recipient validation", func(t *testing.T) {
		// Test that empty recipient would create an invalid message
		msg := EmailMessage{
			To:      []string{""}, // Empty string recipient
			Subject: "Test Email from Web Dashboard",
			Body:    "This is a test email to verify that the Gmail integration is working correctly using the Gmail API v1.",
			IsHTML:  false,
		}

		// This would fail validation in SendEmail
		assert.Contains(t, msg.To, "")
		assert.Len(t, msg.To, 1)
		assert.Equal(t, "Test Email from Web Dashboard", msg.Subject)
		assert.Contains(t, msg.Body, "test email")
		assert.False(t, msg.IsHTML)

		// Test with truly empty recipients array
		emptyMsg := EmailMessage{
			To:      []string{},
			Subject: "Test",
			Body:    "Test",
			IsHTML:  false,
		}

		service := &GmailService{fromEmail: "<EMAIL>"}
		err := service.SendEmail(emptyMsg)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "no recipients specified")
	})
}

// Test email validation and edge cases
func TestEmailValidation(t *testing.T) {
	t.Run("email message validation", func(t *testing.T) {
		// Test various email message configurations
		testCases := []struct {
			name        string
			msg         EmailMessage
			expectError bool
		}{
			{
				name: "valid email",
				msg: EmailMessage{
					To:      []string{"<EMAIL>"},
					Subject: "Test",
					Body:    "Test body",
					IsHTML:  false,
				},
				expectError: false,
			},
			{
				name: "empty recipients",
				msg: EmailMessage{
					To:      []string{},
					Subject: "Test",
					Body:    "Test body",
					IsHTML:  false,
				},
				expectError: true,
			},
			{
				name: "nil recipients",
				msg: EmailMessage{
					To:      nil,
					Subject: "Test",
					Body:    "Test body",
					IsHTML:  false,
				},
				expectError: true,
			},
		}

		service := &GmailService{fromEmail: "<EMAIL>"}

		for _, tc := range testCases {
			t.Run(tc.name, func(t *testing.T) {
				if tc.expectError {
					// Test validation logic directly
					if len(tc.msg.To) == 0 {
						err := service.SendEmail(tc.msg)
						assert.Error(t, err)
						assert.Contains(t, err.Error(), "no recipients specified")
					}
				} else {
					// Test that message structure is valid
					assert.NotEmpty(t, tc.msg.To)
					assert.NotEmpty(t, tc.msg.Subject)

					// Test that we can create a Gmail message from it
					gmailMsg := service.createGmailMessage(tc.msg)
					assert.NotNil(t, gmailMsg)
					assert.NotEmpty(t, gmailMsg.Raw)
				}
			})
		}
	})
}

// Test environment variable handling
func TestEnvironmentVariables(t *testing.T) {
	t.Run("environment variable precedence", func(t *testing.T) {
		// Test that environment variables are properly read
		originalVars := map[string]string{
			"GMAIL_SERVICE_ACCOUNT_EMAIL": os.Getenv("GMAIL_SERVICE_ACCOUNT_EMAIL"),
			"GMAIL_PRIVATE_KEY":           os.Getenv("GMAIL_PRIVATE_KEY"),
			"GMAIL_CLIENT_EMAIL":          os.Getenv("GMAIL_CLIENT_EMAIL"),
			"GMAIL_TOKEN_URI":             os.Getenv("GMAIL_TOKEN_URI"),
		}

		defer func() {
			for key, value := range originalVars {
				os.Setenv(key, value)
			}
		}()

		// Test with all variables set
		os.Setenv("GMAIL_SERVICE_ACCOUNT_EMAIL", "<EMAIL>")
		os.Setenv("GMAIL_PRIVATE_KEY", "test-key")
		os.Setenv("GMAIL_CLIENT_EMAIL", "<EMAIL>")
		os.Setenv("GMAIL_TOKEN_URI", "https://custom.token.uri")

		// This will fail due to invalid key, but we can verify the variables are read
		_, err := NewGmailService()
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "failed to parse private key")
	})
}
