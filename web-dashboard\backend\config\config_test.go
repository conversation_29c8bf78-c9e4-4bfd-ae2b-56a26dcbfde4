package config

import (
	"os"
	"testing"

	"github.com/stretchr/testify/assert"
)

// MockCacheManager implements CacheManager interface for testing
type MockCacheManager struct {
	cache map[string]string
	err   error
}

func NewMockCacheManager() *MockCacheManager {
	return &MockCacheManager{
		cache: make(map[string]string),
	}
}

func (m *MockCacheManager) GetSystemSettingFromCache(key string) (string, error) {
	if m.err != nil {
		return "", m.err
	}
	value, exists := m.cache[key]
	if !exists {
		return "", assert.AnError // Simulate cache miss
	}
	return value, nil
}

func (m *MockCacheManager) SetSystemSettingCache(key, value string) error {
	if m.err != nil {
		return m.err
	}
	m.cache[key] = value
	return nil
}

func (m *MockCacheManager) SetError(err error) {
	m.err = err
}

func (m *MockCacheManager) SetCacheValue(key, value string) {
	m.cache[key] = value
}

// MockSystemSettingFetcher for testing database operations
func mockSystemSettingFetcher(key string) (string, error) {
	// Simulate database responses
	switch key {
	case "debug_level":
		return `{"id":1,"key":"debug_level","value":"DEBUG","type":"string"}`, nil
	default:
		return "", assert.AnError
	}
}

func mockSystemSettingFetcherError(key string) (string, error) {
	return "", assert.AnError
}

// Test LogLevel enum and String method
func TestLogLevel(t *testing.T) {
	t.Run("log level constants", func(t *testing.T) {
		assert.Equal(t, LogLevel(0), TRACE)
		assert.Equal(t, LogLevel(1), DEBUG)
		assert.Equal(t, LogLevel(2), INFO)
		assert.Equal(t, LogLevel(3), WARN)
		assert.Equal(t, LogLevel(4), ERROR)
	})

	t.Run("log level string representation", func(t *testing.T) {
		assert.Equal(t, "TRACE", TRACE.String())
		assert.Equal(t, "DEBUG", DEBUG.String())
		assert.Equal(t, "INFO", INFO.String())
		assert.Equal(t, "WARN", WARN.String())
		assert.Equal(t, "ERROR", ERROR.String())
		assert.Equal(t, "UNKNOWN", LogLevel(99).String())
	})
}

// Test parseLogLevel function
func TestParseLogLevel(t *testing.T) {
	tests := []struct {
		input    string
		expected LogLevel
	}{
		{"TRACE", TRACE},
		{"trace", TRACE},
		{"DEBUG", DEBUG},
		{"debug", DEBUG},
		{"INFO", INFO},
		{"info", INFO},
		{"WARN", WARN},
		{"warn", WARN},
		{"WARNING", WARN},
		{"warning", WARN},
		{"ERROR", ERROR},
		{"error", ERROR},
		{"INVALID", -1},
		{"", -1},
	}

	for _, test := range tests {
		t.Run(test.input, func(t *testing.T) {
			result := parseLogLevel(test.input)
			assert.Equal(t, test.expected, result)
		})
	}
}

// Test parseDebugLevelFromJSON function
func TestParseDebugLevelFromJSON(t *testing.T) {
	t.Run("valid JSON with value field", func(t *testing.T) {
		jsonStr := `{"id":1,"key":"debug_level","value":"DEBUG","type":"string"}`
		level := parseDebugLevelFromJSON(jsonStr)
		assert.Equal(t, DEBUG, level)
	})

	t.Run("valid JSON with different log level", func(t *testing.T) {
		jsonStr := `{"id":1,"key":"debug_level","value":"ERROR","type":"string"}`
		level := parseDebugLevelFromJSON(jsonStr)
		assert.Equal(t, ERROR, level)
	})

	t.Run("invalid JSON - fallback to string parsing", func(t *testing.T) {
		jsonStr := `{"id":1,"key":"debug_level","value":"WARN","type":"string"`
		level := parseDebugLevelFromJSON(jsonStr)
		assert.Equal(t, WARN, level)
	})

	t.Run("JSON without value field", func(t *testing.T) {
		jsonStr := `{"id":1,"key":"debug_level","type":"string"}`
		level := parseDebugLevelFromJSON(jsonStr)
		assert.Equal(t, LogLevel(-1), level)
	})

	t.Run("malformed JSON", func(t *testing.T) {
		jsonStr := `not json at all`
		level := parseDebugLevelFromJSON(jsonStr)
		assert.Equal(t, LogLevel(-1), level)
	})

	t.Run("JSON with invalid log level", func(t *testing.T) {
		jsonStr := `{"id":1,"key":"debug_level","value":"INVALID","type":"string"}`
		level := parseDebugLevelFromJSON(jsonStr)
		assert.Equal(t, LogLevel(-1), level)
	})
}

// Test InitDebugConfig function
func TestInitDebugConfig(t *testing.T) {
	t.Run("initialize debug config", func(t *testing.T) {
		mockCache := NewMockCacheManager()

		InitDebugConfig(mockCache, mockSystemSettingFetcher)

		assert.NotNil(t, debugConfig)
		assert.Equal(t, mockCache, debugConfig.cacheManager)
		assert.NotNil(t, debugConfig.systemSettingFetcher)
	})

	t.Run("initialize with nil values", func(t *testing.T) {
		InitDebugConfig(nil, nil)

		assert.NotNil(t, debugConfig)
		assert.Nil(t, debugConfig.cacheManager)
		assert.Nil(t, debugConfig.systemSettingFetcher)
	})
}

// Test GetDebugLevel function
func TestGetDebugLevel(t *testing.T) {
	// Save original environment
	originalLogLevel := os.Getenv("LOG_LEVEL")
	defer os.Setenv("LOG_LEVEL", originalLogLevel)

	t.Run("environment variable takes priority", func(t *testing.T) {
		os.Setenv("LOG_LEVEL", "ERROR")

		level := GetDebugLevel()
		assert.Equal(t, ERROR, level)
	})

	t.Run("invalid environment variable falls back", func(t *testing.T) {
		os.Setenv("LOG_LEVEL", "INVALID")

		// Should fall back to cache/database or default
		level := GetDebugLevel()
		// Could be INFO (default) or value from cache/database
		assert.True(t, level >= TRACE && level <= ERROR)
	})

	t.Run("no environment variable uses cache", func(t *testing.T) {
		os.Unsetenv("LOG_LEVEL")

		mockCache := NewMockCacheManager()
		mockCache.SetCacheValue("debug_level", `{"value":"WARN"}`)
		InitDebugConfig(mockCache, mockSystemSettingFetcher)

		level := GetDebugLevel()
		assert.Equal(t, WARN, level)
	})

	t.Run("cache miss uses database", func(t *testing.T) {
		os.Unsetenv("LOG_LEVEL")

		mockCache := NewMockCacheManager()
		// Don't set cache value to simulate cache miss
		InitDebugConfig(mockCache, mockSystemSettingFetcher)

		level := GetDebugLevel()
		assert.Equal(t, DEBUG, level) // From mockSystemSettingFetcher
	})

	t.Run("no config defaults to INFO", func(t *testing.T) {
		os.Unsetenv("LOG_LEVEL")

		InitDebugConfig(nil, nil)

		level := GetDebugLevel()
		assert.Equal(t, INFO, level)
	})
}

// Test getDebugLevelFromRedis function
func TestGetDebugLevelFromRedis(t *testing.T) {
	t.Run("no debug config", func(t *testing.T) {
		debugConfig = nil

		level := getDebugLevelFromRedis()
		assert.Equal(t, LogLevel(-1), level)
	})

	t.Run("no cache manager", func(t *testing.T) {
		InitDebugConfig(nil, mockSystemSettingFetcher)

		level := getDebugLevelFromRedis()
		assert.Equal(t, LogLevel(-1), level)
	})

	t.Run("cache hit", func(t *testing.T) {
		mockCache := NewMockCacheManager()
		mockCache.SetCacheValue("debug_level", `{"value":"TRACE"}`)
		InitDebugConfig(mockCache, mockSystemSettingFetcher)

		level := getDebugLevelFromRedis()
		assert.Equal(t, TRACE, level)
	})

	t.Run("cache miss, database hit", func(t *testing.T) {
		mockCache := NewMockCacheManager()
		// Don't set cache value to simulate cache miss
		InitDebugConfig(mockCache, mockSystemSettingFetcher)

		level := getDebugLevelFromRedis()
		assert.Equal(t, DEBUG, level) // From mockSystemSettingFetcher

		// Verify value was cached
		cachedValue, err := mockCache.GetSystemSettingFromCache("debug_level")
		assert.NoError(t, err)
		assert.Contains(t, cachedValue, "DEBUG")
	})

	t.Run("cache miss, database miss", func(t *testing.T) {
		mockCache := NewMockCacheManager()
		InitDebugConfig(mockCache, mockSystemSettingFetcherError)

		level := getDebugLevelFromRedis()
		assert.Equal(t, LogLevel(-1), level)
	})
}

// Test ShouldLog function
func TestShouldLog(t *testing.T) {
	// Save original environment
	originalLogLevel := os.Getenv("LOG_LEVEL")
	defer os.Setenv("LOG_LEVEL", originalLogLevel)

	t.Run("log level filtering", func(t *testing.T) {
		os.Setenv("LOG_LEVEL", "WARN")

		assert.False(t, ShouldLog(TRACE))
		assert.False(t, ShouldLog(DEBUG))
		assert.False(t, ShouldLog(INFO))
		assert.True(t, ShouldLog(WARN))
		assert.True(t, ShouldLog(ERROR))
	})

	t.Run("trace level logs everything", func(t *testing.T) {
		os.Setenv("LOG_LEVEL", "TRACE")

		assert.True(t, ShouldLog(TRACE))
		assert.True(t, ShouldLog(DEBUG))
		assert.True(t, ShouldLog(INFO))
		assert.True(t, ShouldLog(WARN))
		assert.True(t, ShouldLog(ERROR))
	})

	t.Run("error level logs only errors", func(t *testing.T) {
		os.Setenv("LOG_LEVEL", "ERROR")

		assert.False(t, ShouldLog(TRACE))
		assert.False(t, ShouldLog(DEBUG))
		assert.False(t, ShouldLog(INFO))
		assert.False(t, ShouldLog(WARN))
		assert.True(t, ShouldLog(ERROR))
	})
}

// Test GetCurrentLogLevelString function
func TestGetCurrentLogLevelString(t *testing.T) {
	// Save original environment
	originalLogLevel := os.Getenv("LOG_LEVEL")
	defer os.Setenv("LOG_LEVEL", originalLogLevel)

	t.Run("returns current log level as string", func(t *testing.T) {
		os.Setenv("LOG_LEVEL", "DEBUG")

		levelStr := GetCurrentLogLevelString()
		assert.Equal(t, "DEBUG", levelStr)
	})
}

// Test error handling and edge cases
func TestConfigErrorHandling(t *testing.T) {
	t.Run("cache error handling", func(t *testing.T) {
		mockCache := NewMockCacheManager()
		mockCache.SetError(assert.AnError)
		InitDebugConfig(mockCache, mockSystemSettingFetcher)

		// Should fall back to database when cache fails
		level := getDebugLevelFromRedis()
		assert.Equal(t, DEBUG, level) // From database fallback
	})

	t.Run("JSON parsing edge cases", func(t *testing.T) {
		testCases := []string{
			`{"value":""}`,      // Empty value
			`{"value":null}`,    // Null value
			`{}`,                // Empty object
			`{"other":"field"}`, // Missing value field
			`"simple string"`,   // Not an object
		}

		for _, testCase := range testCases {
			level := parseDebugLevelFromJSON(testCase)
			assert.Equal(t, LogLevel(-1), level, "Failed for: %s", testCase)
		}
	})
}
