package system_settings

import (
	"os"
	"testing"

	"web-dashboard/backend/database"
	"web-dashboard/backend/testutils"

	"github.com/stretchr/testify/assert"
)

func TestMain(m *testing.M) {
	testutils.SetupTestEnvironment()

	// Initialize database for tests
	err := database.InitDB()
	if err != nil {
		panic("Failed to initialize database for tests: " + err.Error())
	}

	// Run tests
	code := m.Run()

	// Cleanup
	if database.DB != nil {
		database.DB.Close()
	}

	os.Exit(code)
}

// Test SystemSetting struct
func TestSystemSetting(t *testing.T) {
	t.Run("system setting structure", func(t *testing.T) {
		setting := SystemSetting{
			ID:          1,
			Key:         "test_setting",
			Value:       "test_value",
			DataType:    "string",
			Description: "Test setting description",
			Category:    "test",
			IsEncrypted: false,
			CreatedAt:   "2023-01-01 00:00:00",
			UpdatedAt:   "2023-01-01 00:00:00",
		}

		assert.Equal(t, 1, setting.ID)
		assert.Equal(t, "test_setting", setting.Key)
		assert.Equal(t, "test_value", setting.Value)
		assert.Equal(t, "string", setting.DataType)
		assert.Equal(t, "Test setting description", setting.Description)
		assert.Equal(t, "test", setting.Category)
		assert.False(t, setting.IsEncrypted)
		assert.NotEmpty(t, setting.CreatedAt)
		assert.NotEmpty(t, setting.UpdatedAt)
	})

	t.Run("different value types", func(t *testing.T) {
		stringSetting := SystemSetting{Value: "string_value", DataType: "string"}
		intSetting := SystemSetting{Value: 42, DataType: "integer"}
		boolSetting := SystemSetting{Value: true, DataType: "boolean"}
		floatSetting := SystemSetting{Value: 3.14, DataType: "float"}

		assert.Equal(t, "string_value", stringSetting.Value)
		assert.Equal(t, 42, intSetting.Value)
		assert.Equal(t, true, boolSetting.Value)
		assert.Equal(t, 3.14, floatSetting.Value)
	})
}

// Test value conversion functions
func TestConvertValueFromString(t *testing.T) {
	tests := []struct {
		name     string
		value    string
		dataType string
		expected interface{}
	}{
		{"string value", "hello", "string", "hello"},
		{"integer value", "42", "integer", 42},
		{"boolean true", "true", "boolean", true},
		{"boolean false", "false", "boolean", false},
		{"float value", "3.14", "float", 3.14},
		{"json value", `{"key":"value"}`, "json", `{"key":"value"}`},
		{"unknown type", "value", "unknown", "value"},
	}

	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			result := convertValueFromString(test.value, test.dataType)
			assert.Equal(t, test.expected, result)
		})
	}
}

func TestConvertValueFromStringEdgeCases(t *testing.T) {
	t.Run("invalid integer", func(t *testing.T) {
		result := convertValueFromString("not_a_number", "integer")
		assert.Equal(t, "not_a_number", result) // Should fallback to string
	})

	t.Run("invalid boolean", func(t *testing.T) {
		result := convertValueFromString("not_a_bool", "boolean")
		assert.Equal(t, "not_a_bool", result) // Should fallback to string
	})

	t.Run("invalid float", func(t *testing.T) {
		result := convertValueFromString("not_a_float", "float")
		assert.Equal(t, "not_a_float", result) // Should fallback to string
	})

	t.Run("invalid json", func(t *testing.T) {
		result := convertValueFromString("not_json", "json")
		assert.Equal(t, "not_json", result) // Returns raw string for JSON type
	})
}

func TestConvertValueToString(t *testing.T) {
	tests := []struct {
		name     string
		value    interface{}
		dataType string
		expected string
		hasError bool
	}{
		{"string value", "hello", "string", "hello", false},
		{"integer value", 42, "integer", "42", false},
		{"boolean true", true, "boolean", "true", false},
		{"boolean false", false, "boolean", "false", false},
		{"float value", 3.14, "float", "3.14", false},
		{"json value", map[string]string{"key": "value"}, "json", `{"key":"value"}`, false},
	}

	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			result, err := convertValueToString(test.value, test.dataType)
			if test.hasError {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
				assert.Equal(t, test.expected, result)
			}
		})
	}
}

// Test database-dependent functions with error handling
func TestGetSystemSetting(t *testing.T) {
	t.Run("non-existent setting", func(t *testing.T) {
		setting, err := GetSystemSetting("non_existent_setting")
		assert.Error(t, err)
		assert.Nil(t, setting)
		assert.Contains(t, err.Error(), "setting not found")
	})
}

func TestGetAllSystemSettings(t *testing.T) {
	t.Run("get all settings", func(t *testing.T) {
		settings, err := GetAllSystemSettings()
		// Should either return settings or error due to missing tables
		if err != nil {
			assert.Error(t, err)
			assert.Nil(t, settings)
			t.Logf("GetAllSystemSettings failed (expected in test env): %v", err)
		} else {
			assert.NotNil(t, settings)
			assert.GreaterOrEqual(t, len(settings), 0)
		}
	})
}

func TestGetSystemSettingsByCategory(t *testing.T) {
	t.Run("get settings by category", func(t *testing.T) {
		settings, err := GetSystemSettingsByCategory("general")
		// Should either return settings or error due to missing tables
		if err != nil {
			assert.Error(t, err)
			assert.Nil(t, settings)
			t.Logf("GetSystemSettingsByCategory failed (expected in test env): %v", err)
		} else {
			assert.NotNil(t, settings)
			assert.GreaterOrEqual(t, len(settings), 0)
		}
	})
}

func TestCreateOrUpdateSystemSetting(t *testing.T) {
	t.Run("create setting with missing tables", func(t *testing.T) {
		err := CreateOrUpdateSystemSetting("test_key", "test_value", "test", "string")
		// Should error due to missing tables in test environment
		if err != nil {
			assert.Error(t, err)
			t.Logf("CreateOrUpdateSystemSetting failed (expected in test env): %v", err)
		} else {
			// If it succeeds, clean up
			t.Log("CreateOrUpdateSystemSetting succeeded - test database has system_settings table")
		}
	})

	t.Run("invalid value conversion", func(t *testing.T) {
		// Test with a value that can't be converted
		complexValue := make(chan int) // Channels can't be JSON marshaled
		err := CreateOrUpdateSystemSetting("test_key", complexValue, "test", "json")
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "failed to convert value")
	})
}

func TestUpdateSystemSetting(t *testing.T) {
	t.Run("update non-existent setting", func(t *testing.T) {
		err := UpdateSystemSetting("non_existent_key", "value", "test", "string")
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "setting not found")
	})
}

func TestDeleteSystemSetting(t *testing.T) {
	t.Run("delete non-existent setting", func(t *testing.T) {
		err := DeleteSystemSetting("non_existent_key")
		// Should either succeed (no-op) or error due to missing tables
		if err != nil {
			t.Logf("DeleteSystemSetting failed (expected in test env): %v", err)
		}
	})
}

// Test convenience functions
func TestGetSettingValue(t *testing.T) {
	t.Run("get setting value", func(t *testing.T) {
		value, err := GetSettingValue("non_existent_key")
		assert.Error(t, err)
		assert.Nil(t, value)
	})
}

func TestGetSettingString(t *testing.T) {
	t.Run("get string setting with default", func(t *testing.T) {
		result := GetSettingString("non_existent_key", "default_value")
		assert.Equal(t, "default_value", result)
	})
}

func TestGetSettingInt(t *testing.T) {
	t.Run("get int setting with default", func(t *testing.T) {
		result := GetSettingInt("non_existent_key", 42)
		assert.Equal(t, 42, result)
	})
}

func TestGetSettingBool(t *testing.T) {
	t.Run("get bool setting with default", func(t *testing.T) {
		result := GetSettingBool("non_existent_key", true)
		assert.True(t, result)

		result2 := GetSettingBool("non_existent_key", false)
		assert.False(t, result2)
	})
}

// Test data type validation
func TestDataTypeValidation(t *testing.T) {
	t.Run("valid data types", func(t *testing.T) {
		validTypes := []string{"string", "integer", "boolean", "float", "json"}

		for _, dataType := range validTypes {
			// Test that each type can be processed
			assert.NotEmpty(t, dataType)

			// Test conversion functions with each type
			switch dataType {
			case "string":
				result := convertValueFromString("test", dataType)
				assert.Equal(t, "test", result)
			case "integer":
				result := convertValueFromString("123", dataType)
				assert.Equal(t, 123, result)
			case "boolean":
				result := convertValueFromString("true", dataType)
				assert.Equal(t, true, result)
			case "float":
				result := convertValueFromString("3.14", dataType)
				assert.Equal(t, 3.14, result)
			case "json":
				result := convertValueFromString(`{"test":true}`, dataType)
				assert.Equal(t, `{"test":true}`, result) // JSON returns raw string
			}
		}
	})
}

// Test edge cases and error conditions
func TestSystemSettingsEdgeCases(t *testing.T) {
	t.Run("empty key", func(t *testing.T) {
		err := CreateOrUpdateSystemSetting("", "value", "test", "string")
		// Should be handled by database constraints or validation
		if err == nil {
			t.Log("Empty key was allowed - consider adding validation")
		}
	})

	t.Run("empty category", func(t *testing.T) {
		err := CreateOrUpdateSystemSetting("test_key", "value", "", "string")
		// Should be handled appropriately
		if err == nil {
			t.Log("Empty category was allowed")
		}
	})

	t.Run("invalid data type", func(t *testing.T) {
		// Test with an unsupported data type
		result := convertValueFromString("value", "unsupported_type")
		assert.Equal(t, "value", result) // Should fallback to string
	})
}
