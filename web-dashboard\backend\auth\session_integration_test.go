package auth

import (
	"sync"
	"testing"

	"web-dashboard/backend/database"
	"web-dashboard/backend/session"
	"web-dashboard/backend/users"

	"github.com/alicebob/miniredis/v2"
	"github.com/go-redis/redis/v8"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

// setupTestRedis creates a test Redis instance using miniredis
func setupTestRedis(t *testing.T) *session.RedisSessionManager {
	t.Helper()

	// Create a miniredis server
	mr, err := miniredis.Run()
	require.NoError(t, err)

	// Create Redis client pointing to miniredis
	client := redis.NewClient(&redis.Options{
		Addr: mr.Addr(),
	})

	// Create session manager with test Redis
	manager := session.NewRedisSessionManager(client)

	// Cleanup function
	t.Cleanup(func() {
		client.Close()
		mr.Close()
	})

	return manager
}

// Helper function to create a test user and clean up any existing one
func createTestUserForSession(t *testing.T, email string) int {
	t.Helper()

	// Ensure database is available
	if database.DB == nil {
		t.Fatal("Database connection is nil in createTestUser")
	}

	// Cleanup any existing test user first
	_, err := database.DB.Exec("DELETE FROM users WHERE email = $1", email)
	if err != nil {
		t.Logf("Warning: Failed to cleanup existing user: %v", err)
	}

	// Create a test user
	userID, err := users.CreateUser(email, "password123", true, true)
	if err != nil {
		t.Fatalf("Failed to create test user %s: %v", email, err)
	}

	t.Logf("Created test user %s with ID %d", email, userID)
	return userID
}

// Helper function to cleanup test user
func cleanupTestUserForSession(t *testing.T, email string) {
	t.Helper()
	database.DB.Exec("DELETE FROM users WHERE email = $1", email)
}

func TestSessionRedisConnection(t *testing.T) {
	manager := setupTestRedis(t)

	t.Run("Redis connection", func(t *testing.T) {
		// Test that Redis is accessible
		assert.NotNil(t, manager, "Session manager should be initialized")
		assert.NotNil(t, manager.Client, "Redis client should be initialized")
	})
}

func TestSessionIDGeneration(t *testing.T) {
	// Test multiple generations to ensure uniqueness
	sessions := make(map[string]bool)

	for i := 0; i < 100; i++ {
		sessionID, err := generateSessionID()
		assert.NoError(t, err)
		assert.NotEmpty(t, sessionID)
		assert.Equal(t, 64, len(sessionID)) // 32 bytes = 64 hex characters

		// Ensure uniqueness
		assert.False(t, sessions[sessionID], "Session ID should be unique")
		sessions[sessionID] = true
	}
}

func TestSessionLifecycleIntegration(t *testing.T) {
	manager := setupTestRedis(t)

	userEmail := "<EMAIL>"
	userID := createTestUserForSession(t, userEmail)
	defer cleanupTestUserForSession(t, userEmail)

	// Generate session ID
	sessionID, err := generateSessionID()
	require.NoError(t, err)
	assert.NotEmpty(t, sessionID)

	// Create Redis session
	permissions := []string{"Read Access", "Profile Access"}
	clientIP := "127.0.0.1"
	err = manager.CreateSession(sessionID, userID, userEmail, "test", permissions, clientIP)
	require.NoError(t, err)

	// Retrieve session
	sessionData, err := manager.GetSession(sessionID)
	require.NoError(t, err)
	assert.NotNil(t, sessionData)
	assert.Equal(t, userID, sessionData.UserID)
	assert.Equal(t, userEmail, sessionData.Email)
	assert.Equal(t, "test", sessionData.AuthType)
	assert.Equal(t, permissions, sessionData.Permissions)
	assert.Equal(t, clientIP, sessionData.IPAddress)

	// Test session validation
	isValid := manager.IsSessionValid(sessionID)
	assert.True(t, isValid)

	// Test permission checking
	hasPermission, err := manager.HasPermission(sessionID, "Read Access")
	require.NoError(t, err)
	assert.True(t, hasPermission)

	hasPermission, err = manager.HasPermission(sessionID, "Nonexistent Permission")
	require.NoError(t, err)
	assert.False(t, hasPermission)

	// Delete session
	err = manager.DeleteSession(sessionID)
	require.NoError(t, err)

	// Verify session is gone
	isValid = manager.IsSessionValid(sessionID)
	assert.False(t, isValid)

	// Trying to get deleted session should fail
	_, err = manager.GetSession(sessionID)
	assert.Error(t, err)
}

func TestSessionEdgeCasesIntegration(t *testing.T) {
	manager := setupTestRedis(t)

	userEmail := "<EMAIL>"
	userID := createTestUserForSession(t, userEmail)
	defer cleanupTestUserForSession(t, userEmail)

	t.Run("session with unicode characters", func(t *testing.T) {
		sessionToken := "session-🔐-token-测试-🚀"
		permissions := []string{"Read Access"}
		err := manager.CreateSession(sessionToken, userID, userEmail, "test", permissions, "127.0.0.1")
		assert.NoError(t, err)

		sessionData, err := manager.GetSession(sessionToken)
		assert.NoError(t, err)
		assert.Equal(t, userEmail, sessionData.Email)

		manager.DeleteSession(sessionToken)
	})

	t.Run("session with empty permissions", func(t *testing.T) {
		sessionToken := "empty-permissions-session"
		permissions := []string{}
		err := manager.CreateSession(sessionToken, userID, userEmail, "test", permissions, "127.0.0.1")
		assert.NoError(t, err)

		hasPermission, err := manager.HasPermission(sessionToken, "Any Permission")
		require.NoError(t, err)
		assert.False(t, hasPermission)

		manager.DeleteSession(sessionToken)
	})

	t.Run("session with wildcard permission", func(t *testing.T) {
		sessionToken := "wildcard-session"
		permissions := []string{"*"}
		err := manager.CreateSession(sessionToken, userID, userEmail, "test", permissions, "127.0.0.1")
		assert.NoError(t, err)

		hasPermission, err := manager.HasPermission(sessionToken, "Any Permission")
		require.NoError(t, err)
		assert.True(t, hasPermission)

		manager.DeleteSession(sessionToken)
	})
}

func TestSessionConcurrencyIntegration(t *testing.T) {
	manager := setupTestRedis(t)

	userEmail := "<EMAIL>"
	userID := createTestUserForSession(t, userEmail)
	defer cleanupTestUserForSession(t, userEmail)

	// Test concurrent session creation
	t.Run("concurrent session creation", func(t *testing.T) {
		var wg sync.WaitGroup
		numGoroutines := 10
		sessionTokens := make([]string, numGoroutines)
		errors := make([]error, numGoroutines)

		for i := 0; i < numGoroutines; i++ {
			wg.Add(1)
			go func(index int) {
				defer wg.Done()
				sessionID, err := generateSessionID()
				if err != nil {
					errors[index] = err
					return
				}
				sessionTokens[index] = sessionID
				permissions := []string{"Read Access"}
				errors[index] = manager.CreateSession(sessionID, userID, userEmail, "test", permissions, "127.0.0.1")
			}(i)
		}

		wg.Wait()

		// Check that all operations succeeded
		successCount := 0
		for i, err := range errors {
			if err == nil {
				successCount++
				// Clean up successful sessions
				if sessionTokens[i] != "" {
					manager.DeleteSession(sessionTokens[i])
				}
			}
		}

		assert.Greater(t, successCount, 0, "At least some concurrent operations should succeed")
	})
}

func TestSessionErrorHandlingIntegration(t *testing.T) {
	manager := setupTestRedis(t)

	t.Run("get nonexistent session", func(t *testing.T) {
		_, err := manager.GetSession("nonexistent-session")
		assert.Error(t, err)
	})

	t.Run("delete nonexistent session", func(t *testing.T) {
		err := manager.DeleteSession("nonexistent-session")
		assert.NoError(t, err) // Delete operations are typically idempotent
	})

	t.Run("check permission on nonexistent session", func(t *testing.T) {
		_, err := manager.HasPermission("nonexistent-session", "Some Permission")
		assert.Error(t, err)
	})

	t.Run("validate nonexistent session", func(t *testing.T) {
		isValid := manager.IsSessionValid("nonexistent-session")
		assert.False(t, isValid)
	})
}
