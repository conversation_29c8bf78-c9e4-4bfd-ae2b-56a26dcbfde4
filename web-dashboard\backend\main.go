package main

import (
	"context"
	"encoding/json"
	"fmt"
	"io"
	"log"
	"net/http"
	"os"
	"os/exec"
	"strings"
	"time"

	"web-dashboard/backend/api"
	"web-dashboard/backend/auth"
	"web-dashboard/backend/database"
	"web-dashboard/backend/debug"
	"web-dashboard/backend/email"
	"web-dashboard/backend/session"

	"github.com/go-redis/redis/v8"
	"github.com/gorilla/mux"
	"github.com/gorilla/sessions"
)

// responseWriter wraps http.ResponseWriter to capture status code
type responseWriter struct {
	http.ResponseWriter
	statusCode int
}

func (rw *responseWriter) WriteHeader(code int) {
	rw.statusCode = code
	rw.ResponseWriter.WriteHeader(code)
}

// loggingMiddleware logs all HTTP requests with enhanced structured logging
func loggingMiddleware(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		start := time.Now()

		// Wrap the response writer to capture status code
		wrapped := &responseWriter{w, 200}

		// Get client IP
		clientIP := getClientIPFromRequest(r)

		// Log the incoming request at TRACE level for detailed debugging
		debug.Trace("Incoming request: %s %s from %s", r.Method, r.RequestURI, clientIP)

		// Call the next handler
		next.ServeHTTP(wrapped, r)

		// Calculate duration
		duration := time.Since(start)

		// Log based on response status and duration
		switch {
		case wrapped.statusCode >= 500:
			debug.Error("HTTP %d: %s %s %s %v", wrapped.statusCode, r.Method, r.RequestURI, clientIP, duration)
		case wrapped.statusCode >= 400:
			debug.Warn("HTTP %d: %s %s %s %v", wrapped.statusCode, r.Method, r.RequestURI, clientIP, duration)
		case duration > 5*time.Second:
			debug.Warn("Slow request: %s %s %s %v (status: %d)", r.Method, r.RequestURI, clientIP, duration, wrapped.statusCode)
		case duration > 1*time.Second:
			debug.Info("HTTP %d: %s %s %s %v", wrapped.statusCode, r.Method, r.RequestURI, clientIP, duration)
		default:
			debug.Debug("HTTP %d: %s %s %s %v", wrapped.statusCode, r.Method, r.RequestURI, clientIP, duration)
		}

		// Log security-relevant events
		switch wrapped.statusCode {
		case 401:
			debug.Info("Security event: %s unauthorized access to %s from %s", r.Method, r.RequestURI, clientIP)
		case 403:
			debug.Warn("Security event: %s forbidden access to %s from %s", r.Method, r.RequestURI, clientIP)
		}
	})
}

// getClientIPFromRequest extracts client IP from request headers
func getClientIPFromRequest(r *http.Request) string {
	// Try X-Forwarded-For header first (for proxies)
	if xff := r.Header.Get("X-Forwarded-For"); xff != "" {
		if idx := strings.Index(xff, ","); idx != -1 {
			return strings.TrimSpace(xff[:idx])
		}
		return strings.TrimSpace(xff)
	}

	// Try X-Real-IP header
	if xri := r.Header.Get("X-Real-IP"); xri != "" {
		return strings.TrimSpace(xri)
	}

	// Fall back to RemoteAddr
	if idx := strings.LastIndex(r.RemoteAddr, ":"); idx != -1 {
		return r.RemoteAddr[:idx]
	}
	return r.RemoteAddr
}

func main() {
	// Set up logging to file
	logFile, err := os.OpenFile("app.log", os.O_CREATE|os.O_WRONLY|os.O_APPEND, 0666)
	if err != nil {
		log.Fatalln("Failed to open log file:", err)
	}
	defer logFile.Close()

	// Set up multi-writer to log to both file and console
	multiWriter := io.MultiWriter(os.Stdout, logFile)
	log.SetOutput(multiWriter)
	log.SetFlags(log.Ldate | log.Ltime)

	// Initialize database connection first (required for debug system settings)
	if err := database.InitDB(); err != nil {
		log.Printf("Failed to initialize database: %v", err)
		os.Exit(1)
	}

	// Now we can safely use the debug package
	debug.Info("Starting web dashboard application...")
	debug.Info("Database connection established")

	// Start Redis server if not already running
	debug.Info("Starting Redis server...")
	if err := startRedisServer(); err != nil {
		debug.Warn("Warning: Failed to start Redis server: %v", err)
	}

	// Initialize Redis session manager
	redisURL := os.Getenv("REDIS_URL")
	if redisURL == "" {
		// In Replit, Redis runs on localhost:6379 by default
		redisURL = "redis://0.0.0.0:6379"
		debug.Warn("Using default Redis URL for Replit. Set REDIS_URL environment variable if needed.")
	}

	debug.Info("Initializing Redis session manager...")
	if err := session.InitRedis(redisURL); err != nil {
		debug.Error("Failed to initialize Redis: %v", err)
		os.Exit(1)
	}
	debug.Info("Redis session manager initialized")

	// Populate Redis with all system settings on startup
	debug.Debug("Populating Redis with system settings...")
	if err := populateSystemSettingsCache(); err != nil {
		debug.Warn("Failed to populate system settings cache: %v", err)
		// Don't fail startup if cache population fails
	} else {
		debug.Debug("System settings cache populated successfully")
	}

	// Set cache manager in database package for system settings cache
	database.SetCacheManager(session.Manager)

	// Initialize session store for authentication
	sessionKey := os.Getenv("SESSION_KEY")
	if sessionKey == "" {
		sessionKey = "your-secret-key-change-this-in-production-32-bytes-long"
		debug.Warn("Using default session key. Set SESSION_KEY environment variable in production.")
	}

	// Ensure session key is exactly 32 bytes for AES-256
	if len(sessionKey) < 32 {
		sessionKey = sessionKey + "000000000000000000000000000000000000000000000000"
		sessionKey = sessionKey[:32]
	}

	debug.Debug("Session key length: %d", len(sessionKey))

	store := sessions.NewCookieStore([]byte(sessionKey))

	// Configure session options - development-friendly settings
	store.Options = &sessions.Options{
		Path:     "/",
		MaxAge:   86400 * 7, // 7 days
		HttpOnly: true,
		Secure:   false,                // Set to false for development
		SameSite: http.SameSiteLaxMode, // Use Lax for better compatibility
	}

	debug.Debug("Session store configured with MaxAge: %d", store.Options.MaxAge)

	// Initialize auth
	auth.Init()

	router := mux.NewRouter()

	// Add logging middleware
	router.Use(loggingMiddleware)

	// Serve static assets without authentication (needed for auth page)
	frontendPath := "../frontend/public"

	// Add auth routes (these should not require authentication)
	router.HandleFunc("/auth", auth.AuthHandler).Methods("GET", "POST")
	router.HandleFunc("/login", auth.LocalLoginHandler).Methods("POST")
	router.HandleFunc("/logout", auth.LogoutHandler).Methods("GET", "POST")
	router.HandleFunc("/callback", auth.CallbackHandler).Methods("GET")

	// Protected auth routes
	protectedAuthRouter := router.NewRoute().Subrouter()
	protectedAuthRouter.Use(auth.AuthMiddleware)
	protectedAuthRouter.HandleFunc("/change-password", auth.ChangePasswordHandler).Methods("POST")
	protectedAuthRouter.HandleFunc("/request-password-reset", auth.RequestPasswordResetHandler).Methods("POST")

	// Public password reset routes (doesn't require authentication)
	router.HandleFunc("/reset-password", auth.ResetPasswordHandler).Methods("POST")
	router.HandleFunc("/reset-password", auth.ResetPasswordFormHandler).Methods("GET")
	if _, err := os.Stat(frontendPath); err == nil {
		// Serve CSS and JS files without authentication
		router.PathPrefix("/build/").Handler(http.StripPrefix("/build/", http.FileServer(http.Dir(frontendPath+"/build/"))))
		router.PathPrefix("/global.css").Handler(http.FileServer(http.Dir(frontendPath)))
		router.PathPrefix("/favicon.png").Handler(http.FileServer(http.Dir(frontendPath)))
	}

	// reCAPTCHA site key API route (public endpoint - must be before protected routes)
	router.HandleFunc("/api/recaptcha/sitekey", func(w http.ResponseWriter, r *http.Request) {
		if r.Method != "GET" {
			w.Header().Set("Content-Type", "application/json")
			w.WriteHeader(http.StatusMethodNotAllowed)
			json.NewEncoder(w).Encode(map[string]string{"error": "Method not allowed"})
			return
		}

		siteKey := auth.GetRecaptchaSiteKey()
		if siteKey == "" {
			w.Header().Set("Content-Type", "application/json")
			w.WriteHeader(http.StatusInternalServerError)
			json.NewEncoder(w).Encode(map[string]string{"error": "reCAPTCHA not configured"})
			return
		}

		w.Header().Set("Content-Type", "application/json")
		json.NewEncoder(w).Encode(map[string]string{"siteKey": siteKey})
	}).Methods("GET")

	// Public endpoint to check if reCAPTCHA is enabled (needed for frontend to load reCAPTCHA script)
	router.HandleFunc("/api/system-settings/settings/recaptcha_enabled", func(w http.ResponseWriter, r *http.Request) {
		if r.Method != "GET" {
			w.Header().Set("Content-Type", "application/json")
			w.WriteHeader(http.StatusMethodNotAllowed)
			json.NewEncoder(w).Encode(map[string]string{"error": "Method not allowed"})
			return
		}

		setting, err := database.GetSystemSetting("recaptcha_enabled")
		if err != nil {
			// Default to enabled if setting not found
			w.Header().Set("Content-Type", "application/json")
			json.NewEncoder(w).Encode(map[string]interface{}{
				"key":       "recaptcha_enabled",
				"value":     true,
				"data_type": "boolean",
			})
			return
		}

		w.Header().Set("Content-Type", "application/json")
		json.NewEncoder(w).Encode(setting)
	}).Methods("GET")

	// Protected routes (require authentication)
	protectedRouter := router.NewRoute().Subrouter()
	protectedRouter.Use(auth.AuthMiddleware)

	// API routes (protected)
	apiRouter := protectedRouter.PathPrefix("/api").Subrouter()

	// User info API route
	apiRouter.HandleFunc("/user/info", auth.UserInfoHandler).Methods("GET")

	// TOTP MFA routes
	apiRouter.HandleFunc("/totp/setup", auth.SetupTOTPHandler).Methods("POST")
	apiRouter.HandleFunc("/totp/verify-setup", auth.VerifyTOTPSetupHandler).Methods("POST")
	apiRouter.HandleFunc("/totp/disable", auth.DisableTOTPHandler).Methods("POST")
	apiRouter.HandleFunc("/totp/status", auth.GetTOTPStatusHandler).Methods("GET")

	// User API routes
	userRouter := apiRouter.PathPrefix("/user").Subrouter()
	api.AttachUserRoutes(userRouter)

	// Permissions API routes
	permissionRouter := apiRouter.PathPrefix("/permission").Subrouter()
	api.AttachPermissionRoutes(permissionRouter)

	// Security Groups API routes (primary path)
	securityGroupRouter := apiRouter.PathPrefix("").Subrouter()
	api.AttachSecurityGroupRoutes(securityGroupRouter)

	// System Settings API routes
	systemSettingsRouter := apiRouter.PathPrefix("").Subrouter()
	api.AttachSystemSettingsRoutes(systemSettingsRouter)

	// Redis API routes
	redisRouter := apiRouter.PathPrefix("/redis").Subrouter()
	api.AttachRedisRoutes(redisRouter)

	// Email API routes
	emailRouter := apiRouter.PathPrefix("/email").Subrouter()
	attachEmailRoutes(emailRouter)

	// Serve the main app (protected) - catch all routes and serve index.html for authenticated users
	protectedRouter.PathPrefix("/").HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		// Skip API routes - they should be handled by their specific handlers
		if strings.HasPrefix(r.URL.Path, "/api/") {
			http.NotFound(w, r)
			return
		}

		// For all other routes, serve the SPA index.html
		w.Header().Set("Content-Type", "text/html; charset=utf-8")
		http.ServeFile(w, r, frontendPath+"/index.html")
	})
	debug.Info("Serving protected app from: %s", frontendPath)

	// Add reCAPTCHA routes
	router.HandleFunc("/api/recaptcha/sitekey", auth.GetRecaptchaSiteKeyHandler).Methods("GET")

	// Start server
	port := "5000"
	debug.Info("Server starting on port %s", port)
	if err := http.ListenAndServe("0.0.0.0:"+port, router); err != nil {
		debug.Error("Server failed to start: %v", err)
		os.Exit(1)
	}
}

// attachEmailRoutes adds email-related API routes
func attachEmailRoutes(router *mux.Router) {
	// Send email endpoint
	router.HandleFunc("/send", func(w http.ResponseWriter, r *http.Request) {
		if r.Method != "POST" {
			http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
			return
		}

		// Parse request
		to := r.FormValue("to")
		subject := r.FormValue("subject")
		body := r.FormValue("body")
		isHTML := r.FormValue("html") == "true"

		if to == "" || subject == "" || body == "" {
			http.Error(w, "Missing required fields: to, subject, body", http.StatusBadRequest)
			return
		}

		// Split multiple recipients
		recipients := strings.Split(to, ",")
		for i, recipient := range recipients {
			recipients[i] = strings.TrimSpace(recipient)
		}

		// Create Gmail service
		gmailService, err := email.NewGmailService()
		if err != nil {
			debug.Error("Failed to create Gmail service: %v", err)
			http.Error(w, "Email service not configured", http.StatusInternalServerError)
			return
		}

		// Send email
		msg := email.EmailMessage{
			To:      recipients,
			Subject: subject,
			Body:    body,
			IsHTML:  isHTML,
		}

		err = gmailService.SendEmail(msg)
		if err != nil {
			debug.Error("Failed to send email: %v", err)
			http.Error(w, fmt.Sprintf("Failed to send email: %v", err), http.StatusInternalServerError)
			return
		}

		w.WriteHeader(http.StatusOK)
		json.NewEncoder(w).Encode(map[string]string{"status": "Email sent successfully"})
	}).Methods("POST")

	// Test email endpoint
	router.HandleFunc("/test", func(w http.ResponseWriter, r *http.Request) {
		if r.Method != "POST" {
			http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
			return
		}

		to := r.FormValue("to")
		if to == "" {
			http.Error(w, "Missing required field: to", http.StatusBadRequest)
			return
		}

		// Create Gmail service
		gmailService, err := email.NewGmailService()
		if err != nil {
			debug.Error("Failed to create Gmail service: %v", err)
			http.Error(w, "Email service not configured", http.StatusInternalServerError)
			return
		}

		// Send test email
		err = gmailService.SendTestEmail(to)
		if err != nil {
			debug.Error("Failed to send test email: %v", err)
			http.Error(w, fmt.Sprintf("Failed to send test email: %v", err), http.StatusInternalServerError)
			return
		}

		w.WriteHeader(http.StatusOK)
		json.NewEncoder(w).Encode(map[string]string{"status": "Test email sent successfully"})
	}).Methods("POST")
}

// startRedisServer starts Redis server if not already running
func startRedisServer() error {
	// Check if Redis is already running
	if isRedisRunning() {
		debug.Info("Redis server is already running")
		return nil
	}

	// Start Redis server
	cmd := exec.Command("redis-server", "--daemonize", "yes", "--port", "6379", "--bind", "0.0.0.0")

	// Pass through environment variables including MALLOC if set
	cmd.Env = os.Environ()

	if err := cmd.Run(); err != nil {
		return fmt.Errorf("failed to start Redis server: %v", err)
	}

	// Wait a moment for Redis to start
	time.Sleep(2 * time.Second)

	// Verify Redis started successfully
	if !isRedisRunning() {
		return fmt.Errorf("redis server failed to start properly")
	}

	debug.Info("Redis server started successfully")
	return nil
}

// populateSystemSettingsCache loads all system settings into Redis cache
func populateSystemSettingsCache() error {
	if session.Manager == nil {
		return fmt.Errorf("redis session manager not initialized")
	}

	// Get all system settings from database
	allSettings, err := database.GetAllSystemSettings()
	if err != nil {
		return fmt.Errorf("failed to get system settings from database: %v", err)
	}

	debug.Debug("Found %d system settings to cache", len(allSettings))

	// Cache each setting individually
	cachedCount := 0
	for _, setting := range allSettings {
		// Convert system setting to string for caching
		settingBytes, err := json.Marshal(setting)
		if err != nil {
			debug.Warn("Failed to marshal system setting %s: %v", setting.Key, err)
			continue
		}

		// Store in Redis cache
		err = session.Manager.SetSystemSettingCache(setting.Key, string(settingBytes))
		if err != nil {
			debug.Warn("Failed to cache system setting %s: %v", setting.Key, err)
			continue
		}

		cachedCount++
		debug.Trace("Cached system setting: %s", setting.Key)
	}

	debug.Debug("Successfully cached %d out of %d system settings", cachedCount, len(allSettings))
	return nil
}

// isRedisRunning checks if Redis server is running
func isRedisRunning() bool {
	client := redis.NewClient(&redis.Options{
		Addr:         "0.0.0.0:6379",
		Password:     "",
		DB:           0,
		DialTimeout:  1 * time.Second,
		ReadTimeout:  1 * time.Second,
		WriteTimeout: 1 * time.Second,
	})
	defer client.Close()

	ctx := context.Background()
	_, err := client.Ping(ctx).Result()
	return err == nil
}
